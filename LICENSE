MIT License

Copyright (c) 2025 FibOpenDoor Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## 附加条款

### 使用限制

本软件仅供以下用途使用：

1. **教育和研究**: 网络安全教育、学术研究、技术学习
2. **授权测试**: 在获得明确书面授权的网络环境中进行安全测试
3. **防御目的**: 提升网络安全防御能力和事件响应能力
4. **合规审计**: 符合法律法规要求的安全审计和评估

### 禁止用途

严禁将本软件用于：

1. **非法入侵**: 未经授权访问他人计算机系统或网络
2. **恶意攻击**: 对任何系统进行破坏性攻击或拒绝服务攻击
3. **数据窃取**: 非法获取、复制或传播他人敏感信息
4. **商业犯罪**: 任何形式的网络犯罪或商业欺诈活动
5. **违法行为**: 违反当地法律法规的任何网络安全活动

### 免责声明

1. **使用风险**: 使用者需自行承担使用本软件的所有风险
2. **法律责任**: 使用者需确保遵守所在地区的法律法规
3. **技术支持**: 开发者不提供任何形式的技术支持保证
4. **损害赔偿**: 开发者不对任何直接或间接损失承担责任

### 合规要求

使用本软件时，您需要：

1. **获得授权**: 确保在合法授权的环境中使用
2. **遵守法律**: 严格遵守当地网络安全相关法律法规
3. **道德使用**: 遵循网络安全行业的道德准则
4. **记录留存**: 保留使用记录以备合规检查

### 贡献者协议

通过向本项目贡献代码，您同意：

1. **许可授权**: 将您的贡献以 MIT 许可证授权给项目
2. **原创性**: 确保贡献内容为原创或具有合法使用权
3. **责任承担**: 对您的贡献内容承担相应责任
4. **持续授权**: 授权在项目生命周期内持续有效

### 第三方组件

本项目使用了以下第三方开源组件，请遵守相应的许可证：

- FastAPI (MIT License)
- React (MIT License)
- Ant Design (MIT License)
- SQLAlchemy (MIT License)
- Vite (MIT License)
- ECharts (Apache License 2.0)

### 联系方式

如对许可证条款有疑问，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](../../issues)
- 项目讨论: [GitHub Discussions](../../discussions)

---

**重要提醒**: 请在使用前仔细阅读并理解所有条款。如果您不同意任何条款，请勿使用本软件。
