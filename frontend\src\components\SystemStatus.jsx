import React, { useState, useEffect } from 'react'
import { Card, Alert, Button, Space, Typography, Descriptions, Tag } from 'antd'
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  ReloadOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons'
import { checkBackendHealth, checkNetworkStatus } from '../utils/request'

const { Text: AntText, Title } = Typography

const SystemStatus = ({ onClose }) => {
  const [status, setStatus] = useState({
    network: true,
    backend: null,
    loading: false
  })

  const checkStatus = async () => {
    setStatus(prev => ({ ...prev, loading: true }))
    
    try {
      // 检查网络状态
      const networkStatus = checkNetworkStatus()
      
      // 检查后端服务
      const backendStatus = await checkBackendHealth()
      
      setStatus({
        network: networkStatus,
        backend: backendStatus,
        loading: false
      })
    } catch (error) {
      console.error('状态检查失败:', error)
      setStatus(prev => ({ 
        ...prev, 
        backend: { success: false, error: error.message },
        loading: false 
      }))
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  const getStatusIcon = (isSuccess) => {
    return isSuccess ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
    )
  }

  const getStatusTag = (isSuccess, successText = '正常', errorText = '异常') => {
    return (
      <Tag color={isSuccess ? 'success' : 'error'}>
        {isSuccess ? successText : errorText}
      </Tag>
    )
  }

  const renderSolutions = () => {
    const solutions = []

    if (!status.network) {
      solutions.push({
        title: '网络连接问题',
        description: '您的设备似乎没有网络连接',
        actions: ['检查网络连接', '重新连接 WiFi', '检查网络设置']
      })
    }

    if (status.backend && !status.backend.success) {
      solutions.push({
        title: '后端服务连接问题',
        description: '无法连接到后端服务',
        actions: [
          '确认后端服务已启动 (python main.py)',
          '检查端口 8000 是否被占用',
          '查看后端服务日志',
          '检查防火墙设置'
        ]
      })
    }

    return solutions.map((solution, index) => (
      <Alert
        key={index}
        type="warning"
        showIcon
        message={solution.title}
        description={
          <div>
            <p>{solution.description}</p>
            <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
              {solution.actions.map((action, actionIndex) => (
                <li key={actionIndex}>{action}</li>
              ))}
            </ul>
          </div>
        }
        style={{ marginBottom: '16px' }}
      />
    ))
  }

  const hasIssues = !status.network || (status.backend && !status.backend.success)

  return (
    <Card
      title={
        <Space>
          <InfoCircleOutlined />
          系统状态检查
        </Space>
      }
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={checkStatus}
            loading={status.loading}
            size="small"
          >
            重新检查
          </Button>
          {onClose && (
            <Button size="small" onClick={onClose}>
              关闭
            </Button>
          )}
        </Space>
      }
      style={{ marginBottom: '16px' }}
    >
      <Descriptions column={1} size="small">
        <Descriptions.Item 
          label={
            <Space>
              {getStatusIcon(status.network)}
              网络连接
            </Space>
          }
        >
          {getStatusTag(status.network, '已连接', '未连接')}
          {!status.network && (
            <AntText type="secondary" style={{ marginLeft: '8px' }}>
              请检查您的网络连接
            </AntText>
          )}
        </Descriptions.Item>

        <Descriptions.Item 
          label={
            <Space>
              {status.backend ? getStatusIcon(status.backend.success) : <ReloadOutlined spin />}
              后端服务
            </Space>
          }
        >
          {status.backend === null ? (
            <Tag color="processing">检查中...</Tag>
          ) : (
            <>
              {getStatusTag(status.backend.success, '运行中', '连接失败')}
              {status.backend.success ? (
                <AntText type="secondary" style={{ marginLeft: '8px' }}>
                  http://localhost:8000
                </AntText>
              ) : (
                <AntText type="danger" style={{ marginLeft: '8px' }}>
                  {status.backend.error}
                </AntText>
              )}
            </>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="前端服务">
          {getStatusTag(true, '运行中')}
          <AntText type="secondary" style={{ marginLeft: '8px' }}>
            http://localhost:3000
          </AntText>
        </Descriptions.Item>
      </Descriptions>

      {hasIssues && (
        <div style={{ marginTop: '16px' }}>
          <Title level={5}>解决方案</Title>
          {renderSolutions()}
        </div>
      )}

      {!hasIssues && status.backend && (
        <Alert
          type="success"
          showIcon
          message="系统状态正常"
          description="所有服务运行正常，您可以正常使用系统功能。"
          style={{ marginTop: '16px' }}
        />
      )}
    </Card>
  )
}

export default SystemStatus
