import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  Tooltip,
  Badge,
  Descriptions,
  Modal,
  message
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  SafetyOutlined,
  ExclamationCircleOutlined,
  AlertOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text: AntText, Paragraph } = Typography
const { Option } = Select

const ThreatList = () => {
  const [threats, setThreats] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [threatTypeFilter, setThreatTypeFilter] = useState('all')
  const [threatLevelFilter, setThreatLevelFilter] = useState('all')
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedThreat, setSelectedThreat] = useState(null)

  useEffect(() => {
    fetchThreats()
  }, [])

  const fetchThreats = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockThreats = [
        {
          id: 1,
          threat_id: 'threat_001',
          target_host: '*************',
          target_port: 4444,
          threat_type: 'cobalt_strike',
          threat_level: 'critical',
          threat_name: 'CobaltStrike Beacon',
          description: '检测到CobaltStrike Beacon通信，可能存在APT攻击',
          confidence_score: 95,
          is_active: true,
          is_confirmed: true,
          first_seen: dayjs().subtract(2, 'hour'),
          last_seen: dayjs().subtract(5, 'minute'),
          cs_version: '4.5',
          cs_watermark: '305419896',
          indicators: {
            'User-Agent': 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0)',
            'C2_Server': '*************:4444',
            'Sleep_Time': '60000'
          }
        },
        {
          id: 2,
          threat_id: 'threat_002',
          target_host: '*************',
          target_port: 80,
          threat_type: 'webshell',
          threat_level: 'high',
          threat_name: 'PHP WebShell',
          description: '发现PHP WebShell后门，攻击者可能已获得服务器控制权',
          confidence_score: 88,
          is_active: true,
          is_confirmed: false,
          first_seen: dayjs().subtract(4, 'hour'),
          last_seen: dayjs().subtract(10, 'minute'),
          webshell_type: 'PHP',
          webshell_path: '/uploads/shell.php',
          webshell_password: 'admin',
          indicators: {
            'File_Path': '/var/www/html/uploads/shell.php',
            'File_Size': '2048',
            'Last_Modified': '2025-07-08 12:30:00'
          }
        },
        {
          id: 3,
          threat_id: 'threat_003',
          target_host: '*************',
          target_port: 22,
          threat_type: 'suspicious_service',
          threat_level: 'medium',
          threat_name: '可疑SSH服务',
          description: '检测到异常SSH登录行为，可能存在暴力破解攻击',
          confidence_score: 72,
          is_active: false,
          is_confirmed: false,
          first_seen: dayjs().subtract(1, 'day'),
          last_seen: dayjs().subtract(2, 'hour'),
          indicators: {
            'Failed_Attempts': '156',
            'Source_IPs': ['*******', '*******', '**********'],
            'Attack_Duration': '2 hours'
          }
        }
      ]
      
      setThreats(mockThreats)
    } catch (error) {
      console.error('获取威胁信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getThreatTypeTag = (type) => {
    const typeMap = {
      cobalt_strike: { color: 'red', text: 'CobaltStrike', icon: <AlertOutlined /> },
      webshell: { color: 'orange', text: 'WebShell', icon: <ExclamationCircleOutlined /> },
      backdoor: { color: 'purple', text: '后门', icon: <ExclamationCircleOutlined /> },
      botnet: { color: 'magenta', text: '僵尸网络', icon: <AlertOutlined /> },
      malware: { color: 'red', text: '恶意软件', icon: <AlertOutlined /> },
      suspicious_service: { color: 'yellow', text: '可疑服务', icon: <ExclamationCircleOutlined /> }
    }
    const config = typeMap[type] || { color: 'default', text: type }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  const getThreatLevelTag = (level) => {
    const levelMap = {
      critical: { color: 'red', text: '严重' },
      high: { color: 'orange', text: '高危' },
      medium: { color: 'yellow', text: '中危' },
      low: { color: 'blue', text: '低危' }
    }
    const config = levelMap[level] || { color: 'default', text: level }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const handleCreateCounterAction = async (threatId) => {
    try {
      // 模拟创建反制行动
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('反制行动已创建')
    } catch (error) {
      message.error('创建反制行动失败')
    }
  }

  const showThreatDetail = (threat) => {
    setSelectedThreat(threat)
    setDetailModalVisible(true)
  }

  const columns = [
    {
      title: '威胁ID',
      dataIndex: 'threat_id',
      key: 'threat_id',
      width: 120,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: 'monospace' }}>
            {text.substring(0, 8)}...
          </span>
        </Tooltip>
      )
    },
    {
      title: '威胁名称',
      dataIndex: 'threat_name',
      key: 'threat_name',
      width: 150,
      render: (name, record) => (
        <div>
          <AntText strong>{name}</AntText>
          <div>
            {getThreatTypeTag(record.threat_type)}
          </div>
        </div>
      )
    },
    {
      title: '目标主机',
      key: 'target',
      width: 120,
      render: (_, record) => (
        <div>
          <AntText>{record.target_host}</AntText>
          <div>
            <AntText type="secondary" style={{ fontSize: '12px' }}>
              :{record.target_port}
            </AntText>
          </div>
        </div>
      )
    },
    {
      title: '威胁等级',
      dataIndex: 'threat_level',
      key: 'threat_level',
      width: 100,
      render: getThreatLevelTag
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Badge 
            status={record.is_active ? 'processing' : 'default'} 
            text={record.is_active ? '活跃' : '非活跃'} 
          />
          {record.is_confirmed && (
            <Tag color="green" size="small">已确认</Tag>
          )}
        </Space>
      )
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence_score',
      width: 80,
      render: (score) => (
        <Badge 
          count={`${score}%`} 
          style={{ 
            backgroundColor: score >= 90 ? '#52c41a' : 
                           score >= 70 ? '#faad14' : '#ff4d4f' 
          }} 
        />
      )
    },
    {
      title: '首次发现',
      dataIndex: 'first_seen',
      key: 'first_seen',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '最后活动',
      dataIndex: 'last_seen',
      key: 'last_seen',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => showThreatDetail(record)}
            />
          </Tooltip>
          
          {record.is_active && (
            <Tooltip title="创建反制">
              <Button
                type="text"
                size="small"
                icon={<SafetyOutlined />}
                onClick={() => handleCreateCounterAction(record.threat_id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  const filteredThreats = threats.filter(threat => {
    const matchesSearch = !searchText || 
      threat.threat_name.toLowerCase().includes(searchText.toLowerCase()) ||
      threat.target_host.includes(searchText) ||
      threat.threat_id.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesType = threatTypeFilter === 'all' || threat.threat_type === threatTypeFilter
    const matchesLevel = threatLevelFilter === 'all' || threat.threat_level === threatLevelFilter
    
    return matchesSearch && matchesType && matchesLevel
  })

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            威胁检测
          </Title>
        </div>
        <div className="toolbar-right">
          <Space>
            <Input
              placeholder="搜索威胁名称、主机或ID"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Select
              value={threatTypeFilter}
              onChange={setThreatTypeFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部类型</Option>
              <Option value="cobalt_strike">CobaltStrike</Option>
              <Option value="webshell">WebShell</Option>
              <Option value="backdoor">后门</Option>
              <Option value="botnet">僵尸网络</Option>
              <Option value="malware">恶意软件</Option>
              <Option value="suspicious_service">可疑服务</Option>
            </Select>
            <Select
              value={threatLevelFilter}
              onChange={setThreatLevelFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部等级</Option>
              <Option value="critical">严重</Option>
              <Option value="high">高危</Option>
              <Option value="medium">中危</Option>
              <Option value="low">低危</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchThreats}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredThreats}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredThreats.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 威胁详情模态框 */}
      <Modal
        title="威胁详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          selectedThreat?.is_active && (
            <Button
              key="counter"
              type="primary"
              icon={<SafetyOutlined />}
              onClick={() => {
                handleCreateCounterAction(selectedThreat.threat_id)
                setDetailModalVisible(false)
              }}
            >
              创建反制
            </Button>
          )
        ]}
        width={800}
      >
        {selectedThreat && (
          <div>
            <Descriptions title="基本信息" size="small" column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="威胁ID">
                {selectedThreat.threat_id}
              </Descriptions.Item>
              <Descriptions.Item label="威胁名称">
                {selectedThreat.threat_name}
              </Descriptions.Item>
              <Descriptions.Item label="威胁类型">
                {getThreatTypeTag(selectedThreat.threat_type)}
              </Descriptions.Item>
              <Descriptions.Item label="威胁等级">
                {getThreatLevelTag(selectedThreat.threat_level)}
              </Descriptions.Item>
              <Descriptions.Item label="目标主机">
                {selectedThreat.target_host}:{selectedThreat.target_port}
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                {selectedThreat.confidence_score}%
              </Descriptions.Item>
              <Descriptions.Item label="首次发现">
                {selectedThreat.first_seen.format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="最后活动">
                {selectedThreat.last_seen.format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginBottom: 16 }}>
              <Title level={5}>威胁描述</Title>
              <Paragraph>{selectedThreat.description}</Paragraph>
            </div>

            <div>
              <Title level={5}>威胁指标</Title>
              <Descriptions size="small" column={1}>
                {Object.entries(selectedThreat.indicators || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>
                    {Array.isArray(value) ? value.join(', ') : value}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ThreatList
