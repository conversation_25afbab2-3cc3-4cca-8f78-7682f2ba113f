import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  Tooltip,
  Badge,
  Descriptions,
  Modal,
  Timeline
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EnvironmentOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text: AntText, Paragraph } = Typography
const { Option } = Select

const AttackLogs = () => {
  const [logs, setLogs] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [attackTypeFilter, setAttackTypeFilter] = useState('all')
  const [instanceFilter, setInstanceFilter] = useState('all')
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedLog, setSelectedLog] = useState(null)

  useEffect(() => {
    fetchLogs()
  }, [])

  const fetchLogs = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockLogs = [
        {
          id: 1,
          log_id: 'log_001',
          instance_id: 'honeypot_001',
          instance_name: 'SSH蜜罐-01',
          attacker_ip: '*******',
          attacker_port: 54321,
          attacker_hostname: 'attacker.example.com',
          attacker_geolocation: { country: 'CN', city: 'Beijing' },
          attack_type: 'brute_force',
          attack_description: 'SSH暴力破解尝试',
          attack_success: false,
          session_duration: 45,
          bytes_sent: 1024,
          bytes_received: 512,
          auth_username: 'admin',
          auth_password: '123456',
          executed_commands: [],
          risk_score: 75,
          threat_level: 'medium',
          timestamp: dayjs().subtract(10, 'minute'),
          connection_start: dayjs().subtract(11, 'minute'),
          connection_end: dayjs().subtract(10, 'minute')
        },
        {
          id: 2,
          log_id: 'log_002',
          instance_id: 'honeypot_002',
          instance_name: 'HTTP蜜罐-01',
          attacker_ip: '*******',
          attacker_port: 43210,
          attacker_hostname: null,
          attacker_geolocation: { country: 'US', city: 'New York' },
          attack_type: 'exploit_attempt',
          attack_description: 'Web漏洞利用尝试',
          attack_success: false,
          session_duration: 12,
          bytes_sent: 2048,
          bytes_received: 1024,
          request_method: 'POST',
          request_path: '/admin/login.php',
          request_body: 'username=admin&password=admin',
          response_status: 404,
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          risk_score: 85,
          threat_level: 'high',
          timestamp: dayjs().subtract(30, 'minute'),
          connection_start: dayjs().subtract(30, 'minute'),
          connection_end: dayjs().subtract(30, 'minute')
        },
        {
          id: 3,
          log_id: 'log_003',
          instance_id: 'honeypot_001',
          instance_name: 'SSH蜜罐-01',
          attacker_ip: '**********',
          attacker_port: 32109,
          attacker_hostname: 'scanner.bot.net',
          attacker_geolocation: { country: 'RU', city: 'Moscow' },
          attack_type: 'reconnaissance',
          attack_description: '端口扫描和服务探测',
          attack_success: false,
          session_duration: 5,
          bytes_sent: 256,
          bytes_received: 128,
          risk_score: 45,
          threat_level: 'low',
          timestamp: dayjs().subtract(1, 'hour'),
          connection_start: dayjs().subtract(1, 'hour'),
          connection_end: dayjs().subtract(1, 'hour')
        }
      ]
      
      setLogs(mockLogs)
    } catch (error) {
      console.error('获取攻击日志失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getAttackTypeTag = (type) => {
    const typeMap = {
      brute_force: { color: 'orange', text: '暴力破解' },
      exploit_attempt: { color: 'red', text: '漏洞利用' },
      malware_upload: { color: 'purple', text: '恶意软件上传' },
      command_injection: { color: 'magenta', text: '命令注入' },
      sql_injection: { color: 'volcano', text: 'SQL注入' },
      xss_attempt: { color: 'gold', text: 'XSS攻击' },
      reconnaissance: { color: 'blue', text: '侦察扫描' },
      other: { color: 'default', text: '其他' }
    }
    const config = typeMap[type] || { color: 'default', text: type }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getThreatLevelTag = (level) => {
    const levelMap = {
      critical: { color: 'red', text: '严重' },
      high: { color: 'orange', text: '高危' },
      medium: { color: 'yellow', text: '中危' },
      low: { color: 'blue', text: '低危' }
    }
    const config = levelMap[level] || { color: 'default', text: level }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const showLogDetail = (log) => {
    setSelectedLog(log)
    setDetailModalVisible(true)
  }

  const columns = [
    {
      title: '攻击者IP',
      dataIndex: 'attacker_ip',
      key: 'attacker_ip',
      width: 120,
      render: (ip, record) => (
        <div>
          <AntText strong>{ip}</AntText>
          {record.attacker_geolocation && (
            <div>
              <AntText type="secondary" style={{ fontSize: '12px' }}>
                <EnvironmentOutlined /> {record.attacker_geolocation.country}
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '蜜罐实例',
      dataIndex: 'instance_name',
      key: 'instance_name',
      width: 120
    },
    {
      title: '攻击类型',
      dataIndex: 'attack_type',
      key: 'attack_type',
      width: 120,
      render: getAttackTypeTag
    },
    {
      title: '威胁等级',
      dataIndex: 'threat_level',
      key: 'threat_level',
      width: 100,
      render: getThreatLevelTag
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      width: 100,
      render: (score) => (
        <Badge 
          count={score} 
          style={{ 
            backgroundColor: score >= 80 ? '#ff4d4f' : 
                           score >= 60 ? '#faad14' : '#52c41a' 
          }} 
        />
      )
    },
    {
      title: '攻击结果',
      dataIndex: 'attack_success',
      key: 'attack_success',
      width: 100,
      render: (success) => (
        <Tag color={success ? 'red' : 'green'}>
          {success ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '会话时长',
      dataIndex: 'session_duration',
      key: 'session_duration',
      width: 100,
      render: (duration) => `${duration}s`
    },
    {
      title: '攻击时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      render: (_, record) => (
        <Tooltip title="查看详情">
          <Button 
            type="text" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => showLogDetail(record)}
          />
        </Tooltip>
      )
    }
  ]

  const filteredLogs = logs.filter(log => {
    const matchesSearch = !searchText || 
      log.attacker_ip.includes(searchText) ||
      log.attack_description.toLowerCase().includes(searchText.toLowerCase()) ||
      (log.attacker_hostname && log.attacker_hostname.includes(searchText))
    
    const matchesType = attackTypeFilter === 'all' || log.attack_type === attackTypeFilter
    const matchesInstance = instanceFilter === 'all' || log.instance_id === instanceFilter
    
    return matchesSearch && matchesType && matchesInstance
  })

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            攻击日志
          </Title>
        </div>
        <div className="toolbar-right">
          <Space>
            <Input
              placeholder="搜索IP、主机名或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Select
              value={attackTypeFilter}
              onChange={setAttackTypeFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部类型</Option>
              <Option value="brute_force">暴力破解</Option>
              <Option value="exploit_attempt">漏洞利用</Option>
              <Option value="malware_upload">恶意软件上传</Option>
              <Option value="command_injection">命令注入</Option>
              <Option value="sql_injection">SQL注入</Option>
              <Option value="xss_attempt">XSS攻击</Option>
              <Option value="reconnaissance">侦察扫描</Option>
              <Option value="other">其他</Option>
            </Select>
            <Select
              value={instanceFilter}
              onChange={setInstanceFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部蜜罐</Option>
              <Option value="honeypot_001">SSH蜜罐-01</Option>
              <Option value="honeypot_002">HTTP蜜罐-01</Option>
              <Option value="honeypot_003">FTP蜜罐-01</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchLogs}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredLogs}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredLogs.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 攻击日志详情模态框 */}
      <Modal
        title="攻击日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedLog && (
          <div>
            <Descriptions title="攻击者信息" size="small" column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="IP地址">
                {selectedLog.attacker_ip}:{selectedLog.attacker_port}
              </Descriptions.Item>
              <Descriptions.Item label="主机名">
                {selectedLog.attacker_hostname || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="地理位置">
                {selectedLog.attacker_geolocation ? 
                  `${selectedLog.attacker_geolocation.country}, ${selectedLog.attacker_geolocation.city}` : 
                  '未知'
                }
              </Descriptions.Item>
              <Descriptions.Item label="用户代理">
                {selectedLog.user_agent || '未知'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="攻击信息" size="small" column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="攻击类型">
                {getAttackTypeTag(selectedLog.attack_type)}
              </Descriptions.Item>
              <Descriptions.Item label="威胁等级">
                {getThreatLevelTag(selectedLog.threat_level)}
              </Descriptions.Item>
              <Descriptions.Item label="风险评分">
                {selectedLog.risk_score}/100
              </Descriptions.Item>
              <Descriptions.Item label="攻击结果">
                <Tag color={selectedLog.attack_success ? 'red' : 'green'}>
                  {selectedLog.attack_success ? '成功' : '失败'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="会话时长">
                {selectedLog.session_duration}秒
              </Descriptions.Item>
              <Descriptions.Item label="数据传输">
                发送: {selectedLog.bytes_sent}B, 接收: {selectedLog.bytes_received}B
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginBottom: 16 }}>
              <Title level={5}>攻击描述</Title>
              <Paragraph>{selectedLog.attack_description}</Paragraph>
            </div>

            {selectedLog.auth_username && (
              <div style={{ marginBottom: 16 }}>
                <Title level={5}>认证尝试</Title>
                <Descriptions size="small" column={2}>
                  <Descriptions.Item label="用户名">
                    {selectedLog.auth_username}
                  </Descriptions.Item>
                  <Descriptions.Item label="密码">
                    {selectedLog.auth_password}
                  </Descriptions.Item>
                </Descriptions>
              </div>
            )}

            {selectedLog.request_method && (
              <div style={{ marginBottom: 16 }}>
                <Title level={5}>HTTP请求信息</Title>
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="请求方法">
                    {selectedLog.request_method}
                  </Descriptions.Item>
                  <Descriptions.Item label="请求路径">
                    {selectedLog.request_path}
                  </Descriptions.Item>
                  <Descriptions.Item label="请求体">
                    {selectedLog.request_body}
                  </Descriptions.Item>
                  <Descriptions.Item label="响应状态">
                    {selectedLog.response_status}
                  </Descriptions.Item>
                </Descriptions>
              </div>
            )}

            <div>
              <Title level={5}>时间线</Title>
              <Timeline
                items={[
                  {
                    color: 'blue',
                    children: (
                      <div>
                        <AntText strong>连接建立</AntText>
                        <div>
                          <AntText type="secondary">
                            {selectedLog.connection_start.format('YYYY-MM-DD HH:mm:ss')}
                          </AntText>
                        </div>
                      </div>
                    )
                  },
                  {
                    color: selectedLog.attack_success ? 'red' : 'green',
                    children: (
                      <div>
                        <AntText strong>攻击{selectedLog.attack_success ? '成功' : '失败'}</AntText>
                        <div>
                          <AntText type="secondary">
                            {selectedLog.timestamp.format('YYYY-MM-DD HH:mm:ss')}
                          </AntText>
                        </div>
                      </div>
                    )
                  },
                  {
                    color: 'default',
                    children: (
                      <div>
                        <AntText strong>连接断开</AntText>
                        <div>
                          <AntText type="secondary">
                            {selectedLog.connection_end.format('YYYY-MM-DD HH:mm:ss')}
                          </AntText>
                        </div>
                      </div>
                    )
                  }
                ]}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default AttackLogs
