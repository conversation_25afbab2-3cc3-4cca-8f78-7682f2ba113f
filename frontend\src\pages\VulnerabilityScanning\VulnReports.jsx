import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  Tooltip,
  Badge,
  Descriptions,
  Collapse
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text: AntText, Paragraph } = Typography
const { Option } = Select
const { Panel } = Collapse

const VulnReports = () => {
  const [vulnerabilities, setVulnerabilities] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  useEffect(() => {
    fetchVulnerabilities()
  }, [])

  const fetchVulnerabilities = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockVulnerabilities = [
        {
          id: 1,
          vuln_id: 'vuln_001',
          title: 'SQL注入漏洞',
          description: '在登录页面发现SQL注入漏洞，攻击者可能获取数据库敏感信息',
          cve_id: 'CVE-2023-1234',
          severity: 'high',
          cvss_score: 8.5,
          target_host: '*************',
          target_port: 80,
          target_service: 'http',
          target_path: '/login.php',
          vuln_type: 'SQL Injection',
          status: 'open',
          confidence: 95,
          discovered_at: dayjs().subtract(2, 'hour'),
          solution: '使用参数化查询或预编译语句来防止SQL注入攻击'
        },
        {
          id: 2,
          vuln_id: 'vuln_002',
          title: '跨站脚本攻击(XSS)',
          description: '在搜索功能中发现反射型XSS漏洞',
          cve_id: null,
          severity: 'medium',
          cvss_score: 6.1,
          target_host: '*************',
          target_port: 8080,
          target_service: 'http',
          target_path: '/search',
          vuln_type: 'Cross-Site Scripting',
          status: 'confirmed',
          confidence: 88,
          discovered_at: dayjs().subtract(4, 'hour'),
          solution: '对用户输入进行适当的过滤和编码'
        },
        {
          id: 3,
          vuln_id: 'vuln_003',
          title: '目录遍历漏洞',
          description: '发现目录遍历漏洞，可能导致敏感文件泄露',
          cve_id: 'CVE-2023-5678',
          severity: 'critical',
          cvss_score: 9.3,
          target_host: '*************',
          target_port: 443,
          target_service: 'https',
          target_path: '/download',
          vuln_type: 'Directory Traversal',
          status: 'fixed',
          confidence: 92,
          discovered_at: dayjs().subtract(1, 'day'),
          solution: '验证和过滤文件路径参数，使用白名单机制'
        }
      ]
      
      setVulnerabilities(mockVulnerabilities)
    } catch (error) {
      console.error('获取漏洞报告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSeverityTag = (severity, score) => {
    const severityMap = {
      critical: { color: 'red', text: '严重' },
      high: { color: 'orange', text: '高危' },
      medium: { color: 'yellow', text: '中危' },
      low: { color: 'blue', text: '低危' },
      info: { color: 'default', text: '信息' }
    }
    const config = severityMap[severity] || { color: 'default', text: severity }
    return (
      <Space>
        <Tag color={config.color}>{config.text}</Tag>
        {score && <AntText type="secondary">CVSS: {score}</AntText>}
      </Space>
    )
  }

  const getStatusTag = (status) => {
    const statusMap = {
      open: { color: 'red', text: '未修复', icon: <ExclamationCircleOutlined /> },
      confirmed: { color: 'orange', text: '已确认', icon: <ExclamationCircleOutlined /> },
      false_positive: { color: 'default', text: '误报', icon: <CheckCircleOutlined /> },
      fixed: { color: 'green', text: '已修复', icon: <CheckCircleOutlined /> },
      ignored: { color: 'default', text: '已忽略', icon: <CheckCircleOutlined /> }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  const expandedRowRender = (record) => {
    return (
      <div style={{ padding: '16px 0' }}>
        <Descriptions title="漏洞详情" size="small" column={2} style={{ marginBottom: 16 }}>
          <Descriptions.Item label="CVE编号">
            {record.cve_id || '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="置信度">
            {record.confidence}%
          </Descriptions.Item>
          <Descriptions.Item label="漏洞类型">
            {record.vuln_type}
          </Descriptions.Item>
          <Descriptions.Item label="发现时间">
            {record.discovered_at.format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="目标路径" span={2}>
            {record.target_path}
          </Descriptions.Item>
        </Descriptions>

        <div style={{ marginBottom: 16 }}>
          <Title level={5}>漏洞描述</Title>
          <Paragraph>{record.description}</Paragraph>
        </div>

        <div>
          <Title level={5}>修复建议</Title>
          <Paragraph>{record.solution}</Paragraph>
        </div>
      </div>
    )
  }

  const columns = [
    {
      title: '漏洞标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (title, record) => (
        <div>
          <AntText strong>{title}</AntText>
          {record.cve_id && (
            <div>
              <Tag size="small" color="blue">{record.cve_id}</Tag>
            </div>
          )}
        </div>
      )
    },
    {
      title: '严重程度',
      key: 'severity',
      width: 120,
      render: (_, record) => getSeverityTag(record.severity, record.cvss_score)
    },
    {
      title: '目标主机',
      key: 'target',
      width: 150,
      render: (_, record) => (
        <div>
          <AntText>{record.target_host}</AntText>
          <div>
            <AntText type="secondary" style={{ fontSize: '12px' }}>
              {record.target_service}:{record.target_port}
            </AntText>
          </div>
        </div>
      )
    },
    {
      title: '漏洞类型',
      dataIndex: 'vuln_type',
      key: 'vuln_type',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 80,
      render: (confidence) => (
        <Badge 
          count={`${confidence}%`} 
          style={{ 
            backgroundColor: confidence >= 90 ? '#52c41a' : 
                           confidence >= 70 ? '#faad14' : '#ff4d4f' 
          }} 
        />
      )
    },
    {
      title: '发现时间',
      dataIndex: 'discovered_at',
      key: 'discovered_at',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="导出报告">
            <Button 
              type="text" 
              size="small" 
              icon={<DownloadOutlined />}
              onClick={() => {/* 导出报告 */}}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const filteredVulnerabilities = vulnerabilities.filter(vuln => {
    const matchesSearch = !searchText || 
      vuln.title.toLowerCase().includes(searchText.toLowerCase()) ||
      vuln.target_host.includes(searchText) ||
      (vuln.cve_id && vuln.cve_id.toLowerCase().includes(searchText.toLowerCase()))
    
    const matchesSeverity = severityFilter === 'all' || vuln.severity === severityFilter
    const matchesStatus = statusFilter === 'all' || vuln.status === statusFilter
    
    return matchesSearch && matchesSeverity && matchesStatus
  })

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            漏洞报告
          </Title>
        </div>
        <div className="toolbar-right">
          <Space>
            <Input
              placeholder="搜索漏洞标题、主机或CVE"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Select
              value={severityFilter}
              onChange={setSeverityFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部严重程度</Option>
              <Option value="critical">严重</Option>
              <Option value="high">高危</Option>
              <Option value="medium">中危</Option>
              <Option value="low">低危</Option>
              <Option value="info">信息</Option>
            </Select>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部状态</Option>
              <Option value="open">未修复</Option>
              <Option value="confirmed">已确认</Option>
              <Option value="fixed">已修复</Option>
              <Option value="false_positive">误报</Option>
              <Option value="ignored">已忽略</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchVulnerabilities}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredVulnerabilities}
          rowKey="id"
          loading={loading}
          expandable={{
            expandedRowRender,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={e => onExpand(record, e)}
              >
                {expanded ? '收起' : '详情'}
              </Button>
            )
          }}
          pagination={{
            total: filteredVulnerabilities.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>
    </div>
  )
}

export default VulnReports
