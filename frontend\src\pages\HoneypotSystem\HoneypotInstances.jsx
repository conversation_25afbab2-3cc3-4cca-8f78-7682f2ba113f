import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Typography,
  Tooltip,
  Popconfirm,
  Badge
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text: AntText } = Typography
const { Option } = Select
const { TextArea } = Input

const HoneypotInstances = () => {
  const [instances, setInstances] = useState([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchInstances()
  }, [])

  const fetchInstances = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockInstances = [
        {
          id: 1,
          instance_id: 'honeypot_001',
          name: 'SSH蜜罐-01',
          honeypot_type: 'ssh_server',
          bind_ip: '0.0.0.0',
          bind_port: 2222,
          external_ip: '*************',
          external_port: 22,
          status: 'running',
          total_connections: 156,
          total_attacks: 23,
          unique_attackers: 8,
          created_at: dayjs().subtract(2, 'day'),
          started_at: dayjs().subtract(1, 'day'),
          last_activity: dayjs().subtract(5, 'minute')
        },
        {
          id: 2,
          instance_id: 'honeypot_002',
          name: 'HTTP蜜罐-01',
          honeypot_type: 'http_server',
          bind_ip: '0.0.0.0',
          bind_port: 8080,
          external_ip: '*************',
          external_port: 80,
          status: 'running',
          total_connections: 89,
          total_attacks: 12,
          unique_attackers: 5,
          created_at: dayjs().subtract(1, 'day'),
          started_at: dayjs().subtract(12, 'hour'),
          last_activity: dayjs().subtract(2, 'minute')
        },
        {
          id: 3,
          instance_id: 'honeypot_003',
          name: 'FTP蜜罐-01',
          honeypot_type: 'ftp_server',
          bind_ip: '0.0.0.0',
          bind_port: 2121,
          external_ip: '*************',
          external_port: 21,
          status: 'stopped',
          total_connections: 45,
          total_attacks: 3,
          unique_attackers: 2,
          created_at: dayjs().subtract(3, 'day'),
          started_at: null, // 未启动过
          last_activity: dayjs().subtract(2, 'hour')
        }
      ]
      
      setInstances(mockInstances)
    } catch (error) {
      message.error('获取蜜罐实例失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateInstance = async (values) => {
    try {
      // 模拟创建蜜罐
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('蜜罐实例创建成功')
      setCreateModalVisible(false)
      form.resetFields()
      fetchInstances()
    } catch (error) {
      message.error('创建蜜罐实例失败')
    }
  }

  const handleStartInstance = async (instanceId) => {
    try {
      // 模拟启动蜜罐
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('蜜罐启动成功')
      fetchInstances()
    } catch (error) {
      message.error('启动蜜罐失败')
    }
  }

  const handleStopInstance = async (instanceId) => {
    try {
      // 模拟停止蜜罐
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('蜜罐已停止')
      fetchInstances()
    } catch (error) {
      message.error('停止蜜罐失败')
    }
  }

  const getStatusTag = (status) => {
    const statusMap = {
      starting: { color: 'processing', text: '启动中' },
      running: { color: 'success', text: '运行中' },
      stopped: { color: 'default', text: '已停止' },
      error: { color: 'error', text: '错误' },
      maintenance: { color: 'warning', text: '维护中' }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getHoneypotTypeText = (type) => {
    const typeMap = {
      http_server: 'HTTP服务器',
      ssh_server: 'SSH服务器',
      ftp_server: 'FTP服务器',
      telnet_server: 'Telnet服务器',
      smb_server: 'SMB服务器',
      database_server: '数据库服务器',
      custom_service: '自定义服务'
    }
    return typeMap[type] || type
  }

  const columns = [
    {
      title: '蜜罐名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name, record) => (
        <div>
          <AntText strong>{name}</AntText>
          <div>
            <AntText type="secondary" style={{ fontSize: '12px' }}>
              {getHoneypotTypeText(record.honeypot_type)}
            </AntText>
          </div>
        </div>
      )
    },
    {
      title: '绑定地址',
      key: 'bind_address',
      width: 120,
      render: (_, record) => `${record.bind_ip}:${record.bind_port}`
    },
    {
      title: '外部地址',
      key: 'external_address',
      width: 120,
      render: (_, record) => (
        record.external_ip ? `${record.external_ip}:${record.external_port}` : '-'
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag
    },
    {
      title: '连接统计',
      key: 'connections',
      width: 120,
      render: (_, record) => (
        <div>
          <div>总连接: {record.total_connections}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            攻击: {record.total_attacks}
          </div>
        </div>
      )
    },
    {
      title: '攻击者',
      dataIndex: 'unique_attackers',
      key: 'unique_attackers',
      width: 80,
      render: (count) => (
        <Badge count={count} style={{ backgroundColor: '#faad14' }} />
      )
    },
    {
      title: '最后活动',
      dataIndex: 'last_activity',
      key: 'last_activity',
      width: 120,
      render: (time) => {
        try {
          return time ? dayjs(time).fromNow() : '-'
        } catch (error) {
          console.error('时间格式化错误:', error)
          return '-'
        }
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => {
        try {
          return time ? dayjs(time).format('MM-DD HH:mm') : '-'
        } catch (error) {
          console.error('时间格式化错误:', error)
          return '-'
        }
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看日志">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => {/* 查看日志 */}}
            />
          </Tooltip>
          
          <Tooltip title="配置">
            <Button 
              type="text" 
              size="small" 
              icon={<SettingOutlined />}
              onClick={() => {/* 配置蜜罐 */}}
            />
          </Tooltip>
          
          {record.status === 'stopped' && (
            <Tooltip title="启动">
              <Button 
                type="text" 
                size="small" 
                icon={<PlayCircleOutlined />}
                onClick={() => handleStartInstance(record.instance_id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          
          {record.status === 'running' && (
            <Popconfirm
              title="确定要停止这个蜜罐吗？"
              onConfirm={() => handleStopInstance(record.instance_id)}
            >
              <Tooltip title="停止">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<StopOutlined />}
                  style={{ color: '#faad14' }}
                />
              </Tooltip>
            </Popconfirm>
          )}
          
          {record.status === 'stopped' && (
            <Popconfirm
              title="确定要删除这个蜜罐吗？"
              onConfirm={() => {/* 删除蜜罐 */}}
            >
              <Tooltip title="删除">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            蜜罐实例
          </Title>
        </div>
        <div className="toolbar-right">
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchInstances}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建蜜罐
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={instances}
          rowKey="id"
          loading={loading}
          pagination={{
            total: instances.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 创建蜜罐模态框 */}
      <Modal
        title="创建蜜罐实例"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateInstance}
        >
          <Form.Item
            label="蜜罐名称"
            name="name"
            rules={[{ required: true, message: '请输入蜜罐名称' }]}
          >
            <Input placeholder="SSH蜜罐-01" />
          </Form.Item>

          <Form.Item
            label="蜜罐类型"
            name="honeypot_type"
            rules={[{ required: true, message: '请选择蜜罐类型' }]}
            initialValue="ssh_server"
          >
            <Select>
              <Option value="http_server">HTTP服务器</Option>
              <Option value="ssh_server">SSH服务器</Option>
              <Option value="ftp_server">FTP服务器</Option>
              <Option value="telnet_server">Telnet服务器</Option>
              <Option value="smb_server">SMB服务器</Option>
              <Option value="database_server">数据库服务器</Option>
              <Option value="custom_service">自定义服务</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="绑定端口"
            name="bind_port"
            rules={[{ required: true, message: '请输入绑定端口' }]}
            initialValue={8080}
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea rows={3} placeholder="蜜罐描述信息" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建蜜罐
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default HoneypotInstances
