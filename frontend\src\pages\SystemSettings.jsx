import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Button, 
  Space, 
  Typography, 
  Divider,
  message,
  Row,
  Col
} from 'antd'
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons'

const { Title, Text: AntText } = Typography

const SystemSettings = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSave = async (values) => {
    try {
      setLoading(true)
      
      // 模拟保存配置
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('配置保存成功')
    } catch (error) {
      message.error('保存配置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleReset = () => {
    form.resetFields()
    message.info('配置已重置')
  }

  return (
    <div className="fade-in">
      <Title level={2} style={{ marginBottom: 24 }}>
        系统设置
      </Title>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="扫描配置">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                max_concurrent_scans: 10,
                scan_timeout: 3600,
                masscan_rate: 1000,
                default_ports: '1-65535',
                enable_service_detection: true,
                enable_os_detection: false
              }}
            >
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="最大并发扫描数"
                    name="max_concurrent_scans"
                    help="同时运行的扫描任务数量限制"
                  >
                    <InputNumber min={1} max={50} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="扫描超时时间 (秒)"
                    name="scan_timeout"
                    help="单个扫描任务的最大执行时间"
                  >
                    <InputNumber min={60} max={86400} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="Masscan 扫描速率"
                    name="masscan_rate"
                    help="每秒发送的数据包数量"
                  >
                    <InputNumber min={100} max={10000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="默认端口范围"
                    name="default_ports"
                    help="默认扫描的端口范围"
                  >
                    <Input placeholder="1-65535" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="启用服务检测"
                    name="enable_service_detection"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="启用操作系统检测"
                    name="enable_os_detection"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>蜜罐配置</Title>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="基础端口"
                    name="honeypot_base_port"
                    help="蜜罐服务的起始端口号"
                    initialValue={8080}
                  >
                    <InputNumber min={1024} max={65535} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="最大实例数"
                    name="honeypot_max_instances"
                    help="同时运行的蜜罐实例数量限制"
                    initialValue={50}
                  >
                    <InputNumber min={1} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>反制配置</Title>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="启用自动反制"
                    name="counter_attack_enabled"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="反制延迟 (秒)"
                    name="counter_attack_delay"
                    help="检测到威胁后的反制延迟时间"
                    initialValue={5}
                  >
                    <InputNumber min={0} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item style={{ marginTop: 32 }}>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    保存配置
                  </Button>
                  <Button 
                    onClick={handleReset}
                    icon={<ReloadOutlined />}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="工具状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>Masscan</AntText>
                <AntText type="success">已安装</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>Nmap</AntText>
                <AntText type="success">已安装</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>Xray</AntText>
                <AntText type="warning">未配置</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>fscan</AntText>
                <AntText type="warning">未配置</AntText>
              </div>
            </Space>
          </Card>

          <Card title="系统信息" style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>版本</AntText>
                <AntText>v1.0.0</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>运行时间</AntText>
                <AntText>2天 5小时</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>数据库</AntText>
                <AntText type="success">正常</AntText>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <AntText>Redis</AntText>
                <AntText type="success">正常</AntText>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default SystemSettings
