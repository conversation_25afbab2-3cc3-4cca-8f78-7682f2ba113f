import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  message,
  Alert,
  Spin
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text } = Typography

const HoneypotInstancesDebug = () => {
  const [instances, setInstances] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [debugInfo, setDebugInfo] = useState([])

  const addDebugInfo = (message) => {
    const timestamp = dayjs().format('HH:mm:ss.SSS')
    setDebugInfo(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(`[HoneypotInstancesDebug] ${message}`)
  }

  useEffect(() => {
    addDebugInfo('组件挂载，开始获取数据')
    fetchInstances()
  }, [])

  const fetchInstances = async () => {
    try {
      addDebugInfo('开始获取蜜罐实例数据')
      setLoading(true)
      setError(null)
      
      // 模拟 API 调用
      addDebugInfo('模拟 API 调用延迟')
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      addDebugInfo('创建模拟数据')
      const mockInstances = [
        {
          id: 1,
          instance_id: 'honeypot_001',
          name: 'SSH蜜罐-01',
          honeypot_type: 'ssh_server',
          bind_port: 2222,
          status: 'running',
          total_connections: 156,
          total_attacks: 23,
          unique_attackers: 8,
          created_at: dayjs().subtract(2, 'day'),
          last_activity: dayjs().subtract(5, 'minute')
        },
        {
          id: 2,
          instance_id: 'honeypot_002',
          name: 'HTTP蜜罐-01',
          honeypot_type: 'http_server',
          bind_port: 8080,
          status: 'running',
          total_connections: 89,
          total_attacks: 12,
          unique_attackers: 5,
          created_at: dayjs().subtract(1, 'day'),
          last_activity: dayjs().subtract(2, 'minute')
        }
      ]
      
      addDebugInfo(`成功创建 ${mockInstances.length} 条模拟数据`)
      setInstances(mockInstances)
      addDebugInfo('数据设置完成')
      
    } catch (error) {
      addDebugInfo(`获取数据失败: ${error.message}`)
      console.error('获取蜜罐实例失败:', error)
      setError(error.message)
      message.error('获取蜜罐实例失败')
    } finally {
      setLoading(false)
      addDebugInfo('数据获取流程结束')
    }
  }

  const getStatusTag = (status) => {
    try {
      const statusMap = {
        running: { color: 'success', text: '运行中' },
        stopped: { color: 'default', text: '已停止' },
        error: { color: 'error', text: '错误' }
      }
      const config = statusMap[status] || { color: 'default', text: status }
      return <Tag color={config.color}>{config.text}</Tag>
    } catch (error) {
      addDebugInfo(`状态标签渲染错误: ${error.message}`)
      return <Tag>未知</Tag>
    }
  }

  const getHoneypotTypeText = (type) => {
    try {
      const typeMap = {
        http_server: 'HTTP服务器',
        ssh_server: 'SSH服务器',
        ftp_server: 'FTP服务器'
      }
      return typeMap[type] || type
    } catch (error) {
      addDebugInfo(`类型文本转换错误: ${error.message}`)
      return type || '未知'
    }
  }

  const safeTimeFormat = (time, format = 'MM-DD HH:mm') => {
    try {
      if (!time) return '-'
      const dayjsTime = dayjs.isDayjs(time) ? time : dayjs(time)
      return dayjsTime.format(format)
    } catch (error) {
      addDebugInfo(`时间格式化错误: ${error.message}`)
      return '-'
    }
  }

  const safeTimeFromNow = (time) => {
    try {
      if (!time) return '-'
      const dayjsTime = dayjs.isDayjs(time) ? time : dayjs(time)
      return dayjsTime.fromNow()
    } catch (error) {
      addDebugInfo(`相对时间格式化错误: ${error.message}`)
      return '-'
    }
  }

  const columns = [
    {
      title: '蜜罐名称',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => {
        try {
          return (
            <div>
              <div style={{ fontWeight: 'bold' }}>{name || '未命名'}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {getHoneypotTypeText(record.honeypot_type)}
              </div>
            </div>
          )
        } catch (error) {
          addDebugInfo(`名称列渲染错误: ${error.message}`)
          return <div>渲染错误</div>
        }
      }
    },
    {
      title: '绑定端口',
      dataIndex: 'bind_port',
      key: 'bind_port',
      render: (port) => {
        try {
          return port || '-'
        } catch (error) {
          addDebugInfo(`端口列渲染错误: ${error.message}`)
          return '-'
        }
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        try {
          return getStatusTag(status)
        } catch (error) {
          addDebugInfo(`状态列渲染错误: ${error.message}`)
          return <Tag>错误</Tag>
        }
      }
    },
    {
      title: '最后活动',
      dataIndex: 'last_activity',
      key: 'last_activity',
      render: (time) => {
        try {
          return safeTimeFromNow(time)
        } catch (error) {
          addDebugInfo(`最后活动列渲染错误: ${error.message}`)
          return '-'
        }
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => {
        try {
          return safeTimeFormat(time)
        } catch (error) {
          addDebugInfo(`创建时间列渲染错误: ${error.message}`)
          return '-'
        }
      }
    }
  ]

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Text type="secondary">正在加载蜜罐实例...</Text>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      <Title level={3} style={{ marginBottom: 24 }}>
        蜜罐实例 (调试版)
      </Title>

      {error && (
        <Alert
          type="error"
          message="加载错误"
          description={error}
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text strong>调试信息</Text>
          <Button 
            size="small" 
            onClick={() => setDebugInfo([])}
          >
            清除日志
          </Button>
        </div>
        <div style={{ 
          marginTop: 8, 
          maxHeight: 200, 
          overflow: 'auto', 
          background: '#f5f5f5', 
          padding: 8, 
          fontSize: 12,
          fontFamily: 'monospace'
        }}>
          {debugInfo.length === 0 ? (
            <Text type="secondary">暂无调试信息</Text>
          ) : (
            debugInfo.map((info, index) => (
              <div key={index}>{info}</div>
            ))
          )}
        </div>
      </Card>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Text>数据条数: {instances.length}</Text>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchInstances}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
            >
              创建蜜罐
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={instances}
          rowKey="id"
          loading={loading}
          pagination={{
            total: instances.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onRow={(record) => ({
            onClick: () => addDebugInfo(`点击行: ${record.name}`)
          })}
        />
      </Card>
    </div>
  )
}

export default HoneypotInstancesDebug
