/**
 * Day.js 配置文件
 * 统一配置 dayjs 插件和本地化设置
 */

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import 'dayjs/locale/zh-cn'

// 启用插件
dayjs.extend(relativeTime)
dayjs.extend(duration)
dayjs.extend(utc)
dayjs.extend(timezone)

// 设置中文语言
dayjs.locale('zh-cn')

// 导出配置好的 dayjs
export default dayjs

// 常用的时间格式化函数
export const formatters = {
  // 标准日期时间格式
  datetime: (date) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
  
  // 短日期格式
  date: (date) => dayjs(date).format('YYYY-MM-DD'),
  
  // 时间格式
  time: (date) => dayjs(date).format('HH:mm:ss'),
  
  // 相对时间
  fromNow: (date) => dayjs(date).fromNow(),
  
  // 月日时分格式
  shortDateTime: (date) => dayjs(date).format('MM-DD HH:mm'),
  
  // 持续时间格式化
  duration: (seconds) => {
    const dur = dayjs.duration(seconds, 'seconds')
    if (dur.asHours() >= 1) {
      return `${Math.floor(dur.asHours())}小时${dur.minutes()}分钟`
    } else if (dur.asMinutes() >= 1) {
      return `${dur.minutes()}分钟${dur.seconds()}秒`
    } else {
      return `${dur.seconds()}秒`
    }
  }
}

// 时间范围计算
export const timeRanges = {
  // 获取今天的开始和结束时间
  today: () => ({
    start: dayjs().startOf('day'),
    end: dayjs().endOf('day')
  }),
  
  // 获取昨天的开始和结束时间
  yesterday: () => ({
    start: dayjs().subtract(1, 'day').startOf('day'),
    end: dayjs().subtract(1, 'day').endOf('day')
  }),
  
  // 获取最近N天
  lastDays: (days) => ({
    start: dayjs().subtract(days, 'day').startOf('day'),
    end: dayjs().endOf('day')
  }),
  
  // 获取本周
  thisWeek: () => ({
    start: dayjs().startOf('week'),
    end: dayjs().endOf('week')
  }),
  
  // 获取本月
  thisMonth: () => ({
    start: dayjs().startOf('month'),
    end: dayjs().endOf('month')
  })
}
