import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import ErrorBoundary from './components/ErrorBoundary'
import AppHeader from './components/Layout/AppHeader'
import AppSider from './components/Layout/AppSider'
import Dashboard from './pages/Dashboard'
import DashboardSafe from './pages/DashboardSafe'
import ScanManagement from './pages/ScanManagement'
import ThreatDetection from './pages/ThreatDetection'
import HoneypotSystem from './pages/HoneypotSystem'
import VulnerabilityScanning from './pages/VulnerabilityScanning'
import SystemSettings from './pages/SystemSettings'
import TestTextComponent from './TestTextComponent'

const { Content } = Layout

function App() {
  return (
    <ErrorBoundary>
      <Layout className="app-layout">
        <AppHeader />
        <Layout>
          <AppSider />
          <Layout>
            <Content className="app-content">
              <ErrorBoundary>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/dashboard-safe" element={<DashboardSafe />} />
                  <Route path="/scan/*" element={<ScanManagement />} />
                  <Route path="/threat/*" element={<ThreatDetection />} />
                  <Route path="/honeypot/*" element={<HoneypotSystem />} />
                  <Route path="/vulnerability/*" element={<VulnerabilityScanning />} />
                  <Route path="/settings" element={<SystemSettings />} />
                  <Route path="/test-text" element={<TestTextComponent />} />
                </Routes>
              </ErrorBoundary>
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ErrorBoundary>
  )
}

export default App
