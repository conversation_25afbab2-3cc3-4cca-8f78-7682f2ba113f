import React, { useState, useEffect } from 'react'
import {
  Row,
  Col,
  Card,
  Statistic,
  List,
  Avatar,
  Space,
  Button,
  Typography,
  Spin,
  Alert
} from 'antd'
import {
  RadarChartOutlined,
  ScanOutlined,
  BugOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import dayjs from '../utils/dayjs'
import SystemStatus from '../components/SystemStatus'
import { checkBackendHealth } from '../utils/request'

// 使用别名避免命名冲突
const { Title, Text: AntText } = Typography

const DashboardSafe = () => {
  const [loading, setLoading] = useState(true)
  const [overview, setOverview] = useState({})
  const [recentActivity, setRecentActivity] = useState([])
  const [threatTrend, setThreatTrend] = useState([])
  const [systemStatus, setSystemStatus] = useState({})
  const [showSystemStatus, setShowSystemStatus] = useState(false)
  const [backendConnected, setBackendConnected] = useState(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // 检查后端连接状态
      const healthCheck = await checkBackendHealth()
      setBackendConnected(healthCheck.success)
      
      if (!healthCheck.success) {
        setShowSystemStatus(true)
      }
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟概览数据
      const mockOverview = {
        scans: { total: 156, running: 3, completed: 153 },
        threats: { total: 42, active: 8, resolved: 34 },
        honeypots: { total: 12, running: 8, stopped: 4 },
        vulnerabilities: { total: 89, critical: 5, others: 84 }
      }
      
      // 模拟最近活动
      const mockActivity = [
        {
          id: 1,
          type: 'scan',
          title: '端口扫描完成',
          description: '***********/24 扫描完成，发现 15 个活跃主机',
          timestamp: dayjs().subtract(5, 'minute'),
          status: 'success'
        },
        {
          id: 2,
          type: 'threat',
          title: '检测到威胁',
          description: '发现 CobaltStrike Beacon 通信',
          timestamp: dayjs().subtract(15, 'minute'),
          status: 'warning'
        },
        {
          id: 3,
          type: 'honeypot',
          title: '蜜罐攻击',
          description: 'SSH 蜜罐捕获暴力破解尝试',
          timestamp: dayjs().subtract(30, 'minute'),
          status: 'info'
        }
      ]
      
      // 模拟威胁趋势数据
      const mockThreatTrend = [
        { date: '07-01', threats: 12 },
        { date: '07-02', threats: 8 },
        { date: '07-03', threats: 15 },
        { date: '07-04', threats: 6 },
        { date: '07-05', threats: 20 },
        { date: '07-06', threats: 11 },
        { date: '07-07', threats: 18 }
      ]
      
      // 模拟系统状态
      const mockSystemStatus = {
        cpu_usage: 45.2,
        memory_usage: 62.8,
        disk_usage: 38.1
      }
      
      setOverview(mockOverview)
      setRecentActivity(mockActivity)
      setThreatTrend(mockThreatTrend)
      setSystemStatus(mockSystemStatus)
      
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type) => {
    const iconMap = {
      scan: <RadarChartOutlined style={{ color: '#1890ff' }} />,
      threat: <AlertOutlined style={{ color: '#ff4d4f' }} />,
      honeypot: <ScanOutlined style={{ color: '#52c41a' }} />,
      vulnerability: <BugOutlined style={{ color: '#faad14' }} />
    }
    return iconMap[type] || <UserOutlined />
  }

  const getStatusIcon = (status) => {
    const iconMap = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      warning: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      info: <ClockCircleOutlined style={{ color: '#1890ff' }} />
    }
    return iconMap[status] || <ClockCircleOutlined />
  }

  const renderItem = (item) => (
    <List.Item>
      <List.Item.Meta
        avatar={<Avatar icon={getActivityIcon(item.type)} />}
        title={
          <Space>
            {item.title}
            {getStatusIcon(item.status)}
          </Space>
        }
        description={
          <div>
            <div>{item.description}</div>
            <AntText type="secondary" style={{ fontSize: '12px' }}>
              {item.timestamp.fromNow()}
            </AntText>
          </div>
        }
      />
    </List.Item>
  )

  const threatTrendOption = {
    title: {
      text: '威胁趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: threatTrend.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: threatTrend.map(item => item.threats),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#ff4d4f'
      }
    }]
  }

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh' 
      }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="fade-in">
      <Title level={2} style={{ marginBottom: 24 }}>
        系统仪表板 (安全版本)
      </Title>

      {/* 系统状态检查 */}
      {showSystemStatus && (
        <SystemStatus onClose={() => setShowSystemStatus(false)} />
      )}

      {/* 后端连接状态提示 */}
      {backendConnected === false && !showSystemStatus && (
        <div style={{ marginBottom: 16 }}>
          <Card>
            <Space>
              <ExclamationCircleOutlined style={{ color: '#faad14' }} />
              <AntText>后端服务连接失败，当前显示模拟数据。</AntText>
              <Button 
                type="link" 
                size="small"
                onClick={() => setShowSystemStatus(true)}
              >
                查看详情
              </Button>
            </Space>
          </Card>
        </div>
      )}

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="扫描任务"
              value={overview.scans?.total || 0}
              prefix={<RadarChartOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  运行中: {overview.scans?.running || 0}
                </AntText>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="威胁检测"
              value={overview.threats?.total || 0}
              prefix={<AlertOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  活跃: {overview.threats?.active || 0}
                </AntText>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="蜜罐系统"
              value={overview.honeypots?.total || 0}
              prefix={<ScanOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  运行: {overview.honeypots?.running || 0}
                </AntText>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="漏洞发现"
              value={overview.vulnerabilities?.total || 0}
              prefix={<BugOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  严重: {overview.vulnerabilities?.critical || 0}
                </AntText>
              }
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card 
            title="最近活动" 
            extra={
              <Button 
                type="text" 
                size="small"
                onClick={fetchDashboardData}
              >
                刷新
              </Button>
            }
          >
            <List
              dataSource={recentActivity}
              renderItem={renderItem}
              size="small"
            />
          </Card>
        </Col>

        {/* 威胁趋势 */}
        <Col xs={24} lg={12}>
          <Card title="威胁趋势">
            <ReactECharts 
              option={threatTrendOption} 
              style={{ height: '300px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="系统状态">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Statistic
                  title="CPU 使用率"
                  value={systemStatus.cpu_usage || 0}
                  precision={1}
                  suffix="%"
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="内存使用率"
                  value={systemStatus.memory_usage || 0}
                  precision={1}
                  suffix="%"
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="磁盘使用率"
                  value={systemStatus.disk_usage || 0}
                  precision={1}
                  suffix="%"
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default DashboardSafe
