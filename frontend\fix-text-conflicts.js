/**
 * 修复 Text 组件命名冲突的脚本
 * 
 * 这个脚本会扫描所有 .jsx 文件，找到使用 Text 组件但可能存在命名冲突的地方，
 * 并提供修复建议。
 */

const fs = require('fs')
const path = require('path')

// 扫描目录
const scanDir = './src'

// 需要检查的文件扩展名
const extensions = ['.jsx', '.js']

// 存储问题文件
const issues = []

function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    
    let hasTypographyImport = false
    let hasTextDestructure = false
    let usesTextComponent = false
    
    lines.forEach((line, index) => {
      // 检查是否导入了 Typography
      if (line.includes('import') && line.includes('Typography')) {
        hasTypographyImport = true
      }
      
      // 检查是否解构了 Text
      if (line.includes('Text') && (line.includes('const {') || line.includes('const{'))) {
        hasTextDestructure = true
      }
      
      // 检查是否使用了 Text 组件
      if (line.includes('<Text') || line.includes('Text ')) {
        usesTextComponent = true
      }
    })
    
    // 如果使用了 Text 组件但没有正确导入，记录问题
    if (usesTextComponent && (!hasTypographyImport || !hasTextDestructure)) {
      issues.push({
        file: filePath,
        hasTypographyImport,
        hasTextDestructure,
        usesTextComponent
      })
    }
    
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error.message)
  }
}

function scanDirectory(dir) {
  try {
    const items = fs.readdirSync(dir)
    
    items.forEach(item => {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 等目录
        if (!item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath)
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item)
        if (extensions.includes(ext)) {
          scanFile(fullPath)
        }
      }
    })
  } catch (error) {
    console.error(`扫描目录失败: ${dir}`, error.message)
  }
}

function generateFixSuggestions() {
  console.log('='.repeat(60))
  console.log('Text 组件命名冲突检查报告')
  console.log('='.repeat(60))
  
  if (issues.length === 0) {
    console.log('✅ 没有发现 Text 组件命名冲突问题')
    return
  }
  
  console.log(`❌ 发现 ${issues.length} 个潜在问题文件:\n`)
  
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.file}`)
    console.log(`   - 导入 Typography: ${issue.hasTypographyImport ? '✅' : '❌'}`)
    console.log(`   - 解构 Text: ${issue.hasTextDestructure ? '✅' : '❌'}`)
    console.log(`   - 使用 Text 组件: ${issue.usesTextComponent ? '✅' : '❌'}`)
    
    if (!issue.hasTypographyImport) {
      console.log(`   💡 建议: 添加 Typography 导入`)
      console.log(`      import { Typography } from 'antd'`)
    }
    
    if (!issue.hasTextDestructure) {
      console.log(`   💡 建议: 解构 Text 组件`)
      console.log(`      const { Text } = Typography`)
      console.log(`      或使用别名: const { Text: AntText } = Typography`)
    }
    
    console.log('')
  })
  
  console.log('修复建议:')
  console.log('1. 确保所有使用 Text 组件的文件都正确导入了 Typography')
  console.log('2. 使用别名避免与原生 Text 构造函数冲突: const { Text: AntText } = Typography')
  console.log('3. 或者使用完整路径: Typography.Text')
  console.log('')
}

// 执行扫描
console.log('开始扫描 Text 组件使用情况...')
scanDirectory(scanDir)
generateFixSuggestions()

// 生成修复脚本
const fixScript = `
// 自动修复脚本 (请手动执行)
// 
// 1. 在所有使用 Text 组件的文件中添加正确的导入:
//    import { Typography } from 'antd'
//    const { Text: AntText } = Typography
//
// 2. 将所有 <Text 替换为 <AntText
//
// 3. 或者使用全局修复工具:
//    import { SafeText } from '../utils/components'

${issues.map(issue => `
// 修复文件: ${issue.file}
// 1. 添加导入 (如果缺失):
//    import { Typography } from 'antd'
//    const { Text: AntText } = Typography
// 2. 替换所有 Text 使用为 AntText
`).join('')}
`

fs.writeFileSync('./text-fix-suggestions.txt', fixScript)
console.log('修复建议已保存到 text-fix-suggestions.txt')
