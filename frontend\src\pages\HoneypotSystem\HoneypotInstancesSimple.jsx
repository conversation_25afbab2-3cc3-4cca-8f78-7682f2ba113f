import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  message,
  Spin
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title } = Typography

const HoneypotInstancesSimple = () => {
  const [instances, setInstances] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchInstances()
  }, [])

  const fetchInstances = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 简化的模拟数据
      const mockInstances = [
        {
          id: 1,
          instance_id: 'honeypot_001',
          name: 'SSH蜜罐-01',
          honeypot_type: 'ssh_server',
          bind_port: 2222,
          status: 'running',
          created_at: dayjs().subtract(2, 'day')
        },
        {
          id: 2,
          instance_id: 'honeypot_002',
          name: 'HTTP蜜罐-01',
          honeypot_type: 'http_server',
          bind_port: 8080,
          status: 'stopped',
          created_at: dayjs().subtract(1, 'day')
        }
      ]
      
      setInstances(mockInstances)
    } catch (error) {
      console.error('获取蜜罐实例失败:', error)
      message.error('获取蜜罐实例失败')
    } finally {
      setLoading(false)
    }
  }

  const getStatusTag = (status) => {
    const statusMap = {
      running: { color: 'success', text: '运行中' },
      stopped: { color: 'default', text: '已停止' },
      error: { color: 'error', text: '错误' }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getHoneypotTypeText = (type) => {
    const typeMap = {
      http_server: 'HTTP服务器',
      ssh_server: 'SSH服务器',
      ftp_server: 'FTP服务器'
    }
    return typeMap[type] || type
  }

  const columns = [
    {
      title: '蜜罐名称',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {getHoneypotTypeText(record.honeypot_type)}
          </div>
        </div>
      )
    },
    {
      title: '绑定端口',
      dataIndex: 'bind_port',
      key: 'bind_port'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: () => (
        <Space size="small">
          <Button type="text" size="small">
            查看
          </Button>
          <Button type="text" size="small">
            配置
          </Button>
        </Space>
      )
    }
  ]

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            蜜罐实例 (简化版)
          </Title>
        </div>
        <div className="toolbar-right">
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchInstances}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
          >
            创建蜜罐
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={instances}
          rowKey="id"
          loading={loading}
          pagination={{
            total: instances.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>
    </div>
  )
}

export default HoneypotInstancesSimple
