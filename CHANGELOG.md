# 更新日志

本文档记录了 FibOpenDoor 项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中的功能
- Docker 容器化部署
- 更多蜜罐类型支持
- 机器学习威胁检测
- 分布式扫描支持
- 移动端适配

## [1.0.0] - 2025-07-08

### 新增
- 🎉 项目初始版本发布
- 🏗️ 完整的项目架构设计
- 🖥️ FastAPI 后端框架搭建
- 🌐 React 前端界面开发
- 📊 仪表板和数据可视化
- 🔍 扫描管理模块
- 🛡️ 威胁检测模块
- 🍯 蜜罐系统模块
- 🔎 漏洞扫描模块
- ⚙️ 系统设置模块
- 📚 完整的项目文档

### 后端功能
- ✅ FastAPI 应用框架
- ✅ SQLAlchemy ORM 数据模型
- ✅ 异步数据库支持
- ✅ RESTful API 接口
- ✅ 配置管理系统
- ✅ 日志记录系统
- ✅ 错误处理机制

### 前端功能
- ✅ React 18 + Vite 构建
- ✅ Ant Design UI 组件库
- ✅ ECharts 数据可视化
- ✅ 响应式布局设计
- ✅ 路由管理
- ✅ API 服务集成
- ✅ 全局状态管理

### 数据模型
- ✅ 扫描任务和结果模型
- ✅ 威胁检测和反制模型
- ✅ 蜜罐实例和日志模型
- ✅ 漏洞扫描和报告模型

### 文档
- ✅ README.md 项目说明
- ✅ INSTALL.md 安装指南
- ✅ TROUBLESHOOTING.md 故障排除
- ✅ API.md API 文档
- ✅ CHANGELOG.md 更新日志

### 工具集成 (框架)
- 🔧 Masscan 集成框架
- 🔧 Nmap 集成框架
- 🔧 Xray 集成框架
- 🔧 fscan 集成框架

### 修复
- 🐛 修复图标导入错误
- 🐛 修复 dayjs 时间格式化问题
- 🐛 修复路由配置问题
- 🐛 修复组件导入路径问题

### 优化
- ⚡ 优化前端构建配置
- ⚡ 优化 API 响应结构
- ⚡ 优化数据库查询性能
- ⚡ 优化错误处理机制

### 安全
- 🔒 添加 CORS 中间件
- 🔒 输入验证和清理
- 🔒 SQL 注入防护
- 🔒 XSS 攻击防护

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布类型
- **[未发布]**: 开发中的功能
- **[X.Y.Z]**: 正式发布版本
- **[X.Y.Z-alpha]**: 内测版本
- **[X.Y.Z-beta]**: 公测版本
- **[X.Y.Z-rc]**: 候选发布版本

### 更改类型
- **新增**: 新功能
- **修改**: 对现有功能的修改
- **弃用**: 即将移除的功能
- **移除**: 已移除的功能
- **修复**: 问题修复
- **安全**: 安全相关修复

## 贡献指南

如果您想为项目贡献代码或报告问题：

1. 查看 [贡献指南](README.md#-贡献指南)
2. 提交 [Issue](../../issues) 报告问题
3. 创建 [Pull Request](../../pulls) 贡献代码

## 支持

如果您在使用过程中遇到问题：

1. 查看 [故障排除指南](TROUBLESHOOTING.md)
2. 搜索 [已知问题](../../issues)
3. 提交新的 [Issue](../../issues/new)

---

**感谢所有贡献者的支持！** 🙏
