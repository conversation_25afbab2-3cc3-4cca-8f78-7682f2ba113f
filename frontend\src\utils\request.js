/**
 * 网络请求工具函数
 * 统一处理 API 请求和错误
 */

import { message } from 'antd'

// 检查后端服务是否可用
export const checkBackendHealth = async () => {
  try {
    const response = await fetch('/api/v1/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (response.ok) {
      const data = await response.json()
      return { success: true, data }
    } else {
      return { success: false, error: `HTTP ${response.status}` }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 通用的 API 请求函数
export const apiRequest = async (url, options = {}) => {
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  }

  try {
    const response = await fetch(url, defaultOptions)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return { success: true, data }
  } catch (error) {
    console.error('API 请求失败:', error)
    
    // 根据错误类型显示不同的提示
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      message.error('无法连接到后端服务，请检查服务是否启动')
    } else if (error.message.includes('HTTP 404')) {
      message.error('请求的接口不存在')
    } else if (error.message.includes('HTTP 500')) {
      message.error('服务器内部错误')
    } else {
      message.error(`请求失败: ${error.message}`)
    }
    
    return { success: false, error: error.message }
  }
}

// 模拟 API 请求（用于开发阶段）
export const mockApiRequest = async (mockData, delay = 1000) => {
  try {
    await new Promise(resolve => setTimeout(resolve, delay))
    return { success: true, data: mockData }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 检查网络连接状态
export const checkNetworkStatus = () => {
  return navigator.onLine
}

// 重试机制
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await requestFn()
      if (result.success) {
        return result
      }
      throw new Error(result.error)
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }
}

// 批量请求处理
export const batchRequests = async (requests, concurrency = 3) => {
  const results = []
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency)
    const batchResults = await Promise.allSettled(batch)
    results.push(...batchResults)
  }
  
  return results
}

// 请求缓存
const requestCache = new Map()

export const cachedRequest = async (url, options = {}, cacheTime = 5 * 60 * 1000) => {
  const cacheKey = `${url}_${JSON.stringify(options)}`
  const cached = requestCache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < cacheTime) {
    return cached.data
  }
  
  const result = await apiRequest(url, options)
  
  if (result.success) {
    requestCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    })
  }
  
  return result
}

// 清除请求缓存
export const clearRequestCache = () => {
  requestCache.clear()
}

// 导出默认配置
export const defaultConfig = {
  baseURL: '/api/v1',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
}

// 错误类型定义
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  CLIENT_ERROR: 'CLIENT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
}

// 错误处理器
export const handleError = (error, context = '') => {
  console.error(`${context} 错误:`, error)
  
  let errorType = ErrorTypes.UNKNOWN_ERROR
  let userMessage = '发生未知错误'
  
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    errorType = ErrorTypes.NETWORK_ERROR
    userMessage = '网络连接失败，请检查网络状态'
  } else if (error.message.includes('timeout')) {
    errorType = ErrorTypes.TIMEOUT_ERROR
    userMessage = '请求超时，请稍后重试'
  } else if (error.message.includes('HTTP 5')) {
    errorType = ErrorTypes.SERVER_ERROR
    userMessage = '服务器错误，请稍后重试'
  } else if (error.message.includes('HTTP 4')) {
    errorType = ErrorTypes.CLIENT_ERROR
    userMessage = '请求错误，请检查输入参数'
  }
  
  return {
    type: errorType,
    message: userMessage,
    originalError: error,
  }
}
