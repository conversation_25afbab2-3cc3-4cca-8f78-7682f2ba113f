import React from 'react'
import { Card, Typography, Space, Button } from 'antd'
import { DashboardOutlined, ScanOutlined, AlertOutlined } from '@ant-design/icons'

const { Title, Text: AntText } = Typography

function TestApp() {
  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Card style={{ maxWidth: 600, margin: '0 auto' }}>
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          <DashboardOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          
          <Title level={2}>FibOpenDoor 系统测试</Title>
          
          <AntText>如果您能看到这个页面，说明前端基础环境配置正确！</AntText>
          
          <Space>
            <Button type="primary" icon={<ScanOutlined />}>
              扫描功能
            </Button>
            <Button icon={<AlertOutlined />}>
              威胁检测
            </Button>
          </Space>
          
          <div style={{ marginTop: '24px', padding: '16px', background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
            <AntText type="success">✅ React 组件渲染正常</AntText><br />
            <AntText type="success">✅ Ant Design 组件库加载正常</AntText><br />
            <AntText type="success">✅ 图标组件导入正常</AntText>
          </div>

          <AntText type="secondary">
            接下来您可以启动后端服务，然后访问完整的系统界面
          </AntText>
        </Space>
      </Card>
    </div>
  )
}

export default TestApp
