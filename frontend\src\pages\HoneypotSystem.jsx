import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import HoneypotInstances from './HoneypotSystem/HoneypotInstances'
import HoneypotInstancesSimple from './HoneypotSystem/HoneypotInstancesSimple'
import HoneypotInstancesDebug from './HoneypotSystem/HoneypotInstancesDebug'
import HoneypotInstancesTest from './HoneypotSystem/HoneypotInstancesTest'
import AttackLogs from './HoneypotSystem/AttackLogs'

const HoneypotSystem = () => {
  return (
    <Routes>
      <Route path="/" element={<Navigate to="instances" replace />} />
      <Route path="instances" element={<HoneypotInstances />} />
      <Route path="instances-simple" element={<HoneypotInstancesSimple />} />
      <Route path="instances-debug" element={<HoneypotInstancesDebug />} />
      <Route path="instances-test" element={<HoneypotInstancesTest />} />
      <Route path="logs" element={<AttackLogs />} />
    </Routes>
  )
}

export default HoneypotSystem
