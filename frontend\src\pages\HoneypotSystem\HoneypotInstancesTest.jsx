import React from 'react'
import { Card, Typography, Alert } from 'antd'

const { Title } = Typography

const HoneypotInstancesTest = () => {
  return (
    <div className="fade-in">
      <Title level={2} style={{ marginBottom: 24 }}>
        蜜罐实例 - 测试页面
      </Title>
      
      <Card>
        <Alert
          type="success"
          showIcon
          message="页面加载成功"
          description="如果您能看到这个页面，说明路由和基础组件工作正常。"
          style={{ marginBottom: 16 }}
        />
        
        <p>这是一个简化的测试页面，用于验证蜜罐实例页面的基础功能。</p>
        
        <div style={{ marginTop: 16, padding: 16, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
          <strong>测试结果:</strong>
          <ul style={{ marginTop: 8, marginBottom: 0 }}>
            <li>✅ React 组件渲染正常</li>
            <li>✅ Ant Design 组件加载正常</li>
            <li>✅ 路由导航正常</li>
            <li>✅ CSS 样式应用正常</li>
          </ul>
        </div>
      </Card>
    </div>
  )
}

export default HoneypotInstancesTest
