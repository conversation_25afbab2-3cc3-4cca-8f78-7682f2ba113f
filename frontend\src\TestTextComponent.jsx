import React from 'react'
import { Card, Typography } from 'antd'

// 使用别名避免与原生 Text 构造函数冲突
const { Text: AntText, Title } = Typography

function TestTextComponent() {
  return (
    <Card style={{ margin: '20px' }}>
      <Title level={3}>Text 组件测试</Title>
      <div>
        <AntText>这是一个普通文本</AntText>
      </div>
      <div>
        <AntText type="secondary">这是次要文本</AntText>
      </div>
      <div>
        <AntText type="success">这是成功文本</AntText>
      </div>
      <div>
        <AntText type="warning">这是警告文本</AntText>
      </div>
      <div>
        <AntText type="danger">这是危险文本</AntText>
      </div>
      <div>
        <AntText strong>这是粗体文本</AntText>
      </div>
      <div>
        <AntText italic>这是斜体文本</AntText>
      </div>
      <div>
        <AntText underline>这是下划线文本</AntText>
      </div>
      <div>
        <AntText delete>这是删除线文本</AntText>
      </div>
      <div>
        <AntText code>这是代码文本</AntText>
      </div>
    </Card>
  )
}

export default TestTextComponent
