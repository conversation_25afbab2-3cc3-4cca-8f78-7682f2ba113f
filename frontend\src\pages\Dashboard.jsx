import React, { useState, useEffect } from 'react'
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Table, 
  Tag, 
  Typography, 
  Space,
  Progress,
  List,
  Avatar,
  Spin
} from 'antd'
import {
  RadarChartOutlined,
  ScanOutlined,
  BugOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import dayjs from '../utils/dayjs'
import SystemStatus from '../components/SystemStatus'
import { checkBackendHealth } from '../utils/request'

const { Title, Text: AntText } = Typography

const Dashboard = () => {
  const [loading, setLoading] = useState(true)
  const [overview, setOverview] = useState({})
  const [recentActivity, setRecentActivity] = useState([])
  const [threatTrend, setThreatTrend] = useState([])
  const [systemStatus, setSystemStatus] = useState({})
  const [showSystemStatus, setShowSystemStatus] = useState(false)
  const [backendConnected, setBackendConnected] = useState(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // 检查后端连接状态
      const healthCheck = await checkBackendHealth()
      setBackendConnected(healthCheck.success)

      if (!healthCheck.success) {
        setShowSystemStatus(true)
      }

      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      setOverview({
        scans: { total: 156, running: 3, completed: 153 },
        threats: { total: 42, active: 8, resolved: 34 },
        honeypots: { total: 12, running: 8, stopped: 4 },
        vulnerabilities: { total: 89, critical: 5, others: 84 }
      })

      setRecentActivity([
        {
          id: 1,
          type: 'scan',
          title: '端口扫描完成',
          description: '***********/24 网段扫描已完成，发现 23 个活跃主机',
          timestamp: dayjs().subtract(5, 'minute'),
          status: 'success'
        },
        {
          id: 2,
          type: 'threat',
          title: '检测到高危威胁',
          description: '在 *************:4444 发现 CobaltStrike Beacon',
          timestamp: dayjs().subtract(15, 'minute'),
          status: 'error'
        },
        {
          id: 3,
          type: 'honeypot',
          title: '蜜罐捕获攻击',
          description: 'SSH 蜜罐记录来自 ******* 的暴力破解尝试',
          timestamp: dayjs().subtract(30, 'minute'),
          status: 'warning'
        },
        {
          id: 4,
          type: 'vulnerability',
          title: '漏洞扫描完成',
          description: 'Xray 扫描发现 12 个漏洞，其中 2 个高危',
          timestamp: dayjs().subtract(1, 'hour'),
          status: 'info'
        }
      ])

      setThreatTrend([
        { date: '2025-07-02', count: 5 },
        { date: '2025-07-03', count: 8 },
        { date: '2025-07-04', count: 12 },
        { date: '2025-07-05', count: 6 },
        { date: '2025-07-06', count: 9 },
        { date: '2025-07-07', count: 15 },
        { date: '2025-07-08', count: 11 }
      ])

      setSystemStatus({
        cpu_usage: 45,
        memory_usage: 62,
        disk_usage: 38,
        network_status: 'normal'
      })

    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 威胁趋势图表配置
  const threatTrendOption = {
    title: {
      text: '威胁趋势 (最近7天)',
      textStyle: { fontSize: 14, fontWeight: 'normal' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>威胁数量: {c}'
    },
    xAxis: {
      type: 'category',
      data: threatTrend.map(item => dayjs(item.date).format('MM-DD'))
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: threatTrend.map(item => item.count),
      type: 'line',
      smooth: true,
      areaStyle: {
        opacity: 0.3
      },
      itemStyle: {
        color: '#ff4d4f'
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }

  const getActivityIcon = (type) => {
    const iconMap = {
      scan: <RadarChartOutlined style={{ color: '#1890ff' }} />,
      threat: <AlertOutlined style={{ color: '#ff4d4f' }} />,
      honeypot: <BugOutlined style={{ color: '#faad14' }} />,
      vulnerability: <ScanOutlined style={{ color: '#52c41a' }} />
    }
    return iconMap[type] || <UserOutlined />
  }

  const getStatusIcon = (status) => {
    const iconMap = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      warning: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      info: <ClockCircleOutlined style={{ color: '#1890ff' }} />
    }
    return iconMap[status] || <ClockCircleOutlined />
  }

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="fade-in">
      <Title level={2} style={{ marginBottom: 24 }}>
        系统仪表板
      </Title>

      {/* 系统状态检查 */}
      {showSystemStatus && (
        <SystemStatus onClose={() => setShowSystemStatus(false)} />
      )}

      {/* 后端连接状态提示 */}
      {backendConnected === false && !showSystemStatus && (
        <div style={{ marginBottom: 16 }}>
          <Card>
            <Space>
              <ExclamationCircleOutlined style={{ color: '#faad14' }} />
              <AntText>后端服务连接失败，当前显示模拟数据。</AntText>
              <Button
                type="link"
                size="small"
                onClick={() => setShowSystemStatus(true)}
              >
                查看详情
              </Button>
            </Space>
          </Card>
        </div>
      )}

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="status-card">
            <Statistic
              title="扫描任务"
              value={overview.scans?.total}
              prefix={<RadarChartOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  运行中: {overview.scans?.running}
                </AntText>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="status-card">
            <Statistic
              title="威胁检测"
              value={overview.threats?.total}
              prefix={<ScanOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  活跃: {overview.threats?.active}
                </AntText>
              }
              valueStyle={{ color: overview.threats?.active > 0 ? '#ff4d4f' : undefined }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="status-card">
            <Statistic
              title="蜜罐系统"
              value={overview.honeypots?.total}
              prefix={<BugOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  运行: {overview.honeypots?.running}
                </AntText>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="status-card">
            <Statistic
              title="漏洞发现"
              value={overview.vulnerabilities?.total}
              prefix={<AlertOutlined />}
              suffix={
                <AntText type="secondary" style={{ fontSize: '12px' }}>
                  高危: {overview.vulnerabilities?.critical}
                </AntText>
              }
              valueStyle={{ color: overview.vulnerabilities?.critical > 0 ? '#faad14' : undefined }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 威胁趋势图 */}
        <Col xs={24} lg={16}>
          <Card title="威胁趋势分析" style={{ height: 400 }}>
            <ReactECharts 
              option={threatTrendOption} 
              style={{ height: '320px' }}
            />
          </Card>
        </Col>

        {/* 系统状态 */}
        <Col xs={24} lg={8}>
          <Card title="系统状态" style={{ height: 400 }}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <Text>CPU 使用率</Text>
                <Progress 
                  percent={systemStatus.cpu_usage} 
                  status={systemStatus.cpu_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </div>
              <div>
                <Text>内存使用率</Text>
                <Progress 
                  percent={systemStatus.memory_usage} 
                  status={systemStatus.memory_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </div>
              <div>
                <Text>磁盘使用率</Text>
                <Progress 
                  percent={systemStatus.disk_usage} 
                  status={systemStatus.disk_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </div>
              <div>
                <Text>网络状态</Text>
                <div style={{ marginTop: 8 }}>
                  <Tag color="green">正常</Tag>
                </div>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24}>
          <Card title="最近活动" style={{ marginTop: 16 }}>
            <List
              itemLayout="horizontal"
              dataSource={recentActivity}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar icon={getActivityIcon(item.type)} />
                    }
                    title={
                      <Space>
                        {item.title}
                        {getStatusIcon(item.status)}
                      </Space>
                    }
                    description={
                      <div>
                        <div>{item.description}</div>
                        <AntText type="secondary" style={{ fontSize: '12px' }}>
                          {item.timestamp.fromNow()}
                        </AntText>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
